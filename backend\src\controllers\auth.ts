import type { NextFunction, Request, Response } from "express";
import z from "zod";
import { CustomError } from "../utlis";
import { prisma } from "../prisma";
import { hash } from "bcryptjs"

const

const registerSchema = z.object({
    name: z.string(),
    email: z.string(),
    password: z.string().min(6)
});

export const register = async (req: Request, res: Response, next: NextFunction) => {
    const { name, email, password } = req.body;

    const parsedData = registerSchema.safeParse(req.body);

    // zod validation
    if (!parsedData.success) {
        const error = new CustomError("Invalid Inputs");
        error.statusCode = 400;
        next(error)
        return;
    }
    try {
        const adminExist = await prisma.admin.findUnique({
            where: {
                email: parsedData.data.email
            }
        })

        if (adminExist) {
            const error = new CustomError("Email Address already exists");
            error.statusCode = 400;
            next(error)
            return;
        }

        const hashedPassword = await hash( parsedData.data.password, 10);

        const admin = await prisma.admin.create({
            data: {
                name: parsedData.data.name,
                email: parsedData.data.email,
                password: hashedPassword
            }
        })  

        if (!admin) {
            const error = new CustomError("Something went wrong");
            error.statusCode = 500;
            next(error)
            return;
        }
    } catch (err) {
        const error = new CustomError("Something went wrong");
        error.statusCode = 500;
        next(error)
        return;
    }
}

